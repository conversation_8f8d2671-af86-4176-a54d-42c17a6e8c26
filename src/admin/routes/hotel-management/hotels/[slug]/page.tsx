import { Container, Heading, Text, Badge, <PERSON><PERSON>, Drawer } from "@camped-ai/ui";
import React, { useEffect, useState, useRef } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { HotelData } from "../../../../types";
import { HotelFormData } from "../../../../components/hotel-form";
import HotelFormModern from "../../../../components/hotel-form-modern";
import { toast, Toaster } from "@camped-ai/ui";
import Prompt from "../../../../components/prompt";
import HideSidebarItemsWidget from "../../../../widgets/hide-sidebar-items-widget";
import HotelDashboardMetrics from "../../../../components/hotel/hotel-dashboard-metrics";
import { ChevronLeft } from "@camped-ai/icons";
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  FileText,
  Edit,
  Trash2,
  Hotel,
  Image,
  CheckCircle,
  XCircle,
  Globe,
  Bed,
  CreditCard,
  Hash,
  Flag,
  Calendar,
  BarChart3,
  LogIn,
  LogOut,
  DollarSign,
  PieChart,
  Tags,
  Shield,
} from "lucide-react";
import "./modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import Rating from '@mui/material/Rating';


const HotelDetailPage = () => {
  const { slug } = useParams();
  const [hotel, setHotel] = useState<HotelData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [imageGalleryOpen, setImageGalleryOpen] = useState(false);
  const [roomTypes, setRoomTypes] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [heroImageLoaded, setHeroImageLoaded] = useState(false);
  const [heroImageError, setHeroImageError] = useState(false);
  const navigate = useNavigate();
  const submitRef = useRef<(() => Promise<void>) | null>(null);

  // Enhanced hero image selection with better fallback logic
  const heroImage = React.useMemo(() => {
    if (!hotel?.images || hotel.images.length === 0) {
      return null;
    }

    // First try to find a thumbnail image
    const thumbnailImage = hotel.images.find((img) => img.isThumbnail);
    if (thumbnailImage?.url) {
      console.log("Using thumbnail image:", thumbnailImage.url);
      return thumbnailImage.url;
    }

    // Fallback to first image
    const firstImage = hotel.images[0];
    if (firstImage?.url) {
      console.log("Using first image:", firstImage.url);
      return firstImage.url;
    }

    return null;
  }, [hotel?.images]);

  // Reset loading states when hero image changes
  React.useEffect(() => {
    setHeroImageLoaded(false);
    setHeroImageError(false);
  }, [heroImage]);

  const fetchHotelDetails = async () => {
    try {
      const hotelResponse = await fetch(
        `/admin/hotel-management/hotels/${slug}`,
        {
          credentials: "include",
        }
      );
      const hotelData = await hotelResponse.json();
      const processedHotelData = Array.isArray(hotelData?.hotel)
        ? hotelData?.hotel[0]
        : hotelData;

      // Fetch hotel images
      const imagesResponse = await fetch(
        `/admin/hotel-management/hotels/${processedHotelData.id}/images`,
        {
          credentials: "include",
        }
      );
      const imagesData = await imagesResponse.json();

      const hotelWithImages = {
        ...processedHotelData,
        images: imagesData.images || [],
      };

      setHotel(hotelWithImages);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch hotel details:", error);
      setIsLoading(false);
    }
  };

  const fetchRoomTypes = () => {
    fetch(`/admin/room-types`, {
      credentials: "include",
    })
      .then((res) => res.json())
      .then(({ roomTypes: data }) => {
        setRoomTypes(data);
      });
  };

  useEffect(() => {
    fetchHotelDetails();
    fetchRoomTypes();
  }, [slug]);

  if (isLoading) {
    return (
      <>
        <HideSidebarItemsWidget />
        <div className="h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </>
    );
  }

  if (!hotel) {
    return (
      <>
        <HideSidebarItemsWidget />
        <Container className="py-8">
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <Hotel className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <Text className="text-gray-500 mb-4">Hotel not found</Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => navigate("/hotel-management/hotels")}
            >
              Back to Hotels
            </Button>
          </div>
        </Container>
      </>
    );
  }

  const handleUpdate = async (data: HotelFormData) => {
    try {
      // Separate files to upload from existing URLs
      const mediaArray = data.media ?? [];
      const filesToUpload = mediaArray.filter(
        (media) => media.file instanceof File
      );
      const existingMedia = mediaArray.filter(
        (media) => media.url && !media.file
      );

      // Prepare data for submission
      const dataWithoutMedia = {
        ...data,
        media: existingMedia, // Include existing media in the payload
      };
      delete dataWithoutMedia.image_ids;

      const response = await fetch(`/admin/hotel-management/hotels`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataWithoutMedia),
      });

      const responseData = await response.json();

      if (response.ok) {
        // Upload new files if any
        if (filesToUpload.length > 0) {
          const uploadPromises = filesToUpload.map(async (mediaFile) => {
            if (!(mediaFile.file instanceof File)) {
              throw new Error("Invalid file");
            }

            const formData = new FormData();
            formData.append("files", mediaFile.file);

            try {
              const hotelId = data.id;
              if (!hotelId) {
                throw new Error("No hotel ID available for image upload");
              }

              const response = await fetch(
                `/admin/hotel-management/hotels/${hotelId}/upload`,
                {
                  method: "POST",
                  body: formData,
                  credentials: "include",
                }
              );

              if (!response.ok) {
                const errorText = await response.text();
                console.error("Upload error response:", errorText);
                throw new Error(`File upload failed: ${errorText}`);
              }

              const uploadedFiles = await response.json();
              const uploadedFile = uploadedFiles[0];

              return {
                ...uploadedFile,
                isThumbnail: mediaFile.isThumbnail,
              };
            } catch (error) {
              console.error("File upload error:", error);
              throw error;
            }
          });

          await Promise.all(uploadPromises);
        }

        toast.success("Success", {
          description: "Hotel updated successfully",
        });

        // Refresh hotel details
        fetchHotelDetails();
        return true;
      } else {
        toast.error("Error", {
          description: responseData.message || "Failed to update hotel",
        });
        return false;
      }
    } catch (error) {
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      console.error("Failed to update hotel:", error);
      return false;
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels`, {
        method: "DELETE",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: hotel?.id,
        }),
      });

      if (response.ok) {
        toast.success("Success", {
          description: "Hotel deleted successfully",
        });
        navigate("/hotel-management/hotels");
      } else {
        const data = await response.json();
        toast.error("Error", {
          description: data.message || "Failed to delete hotel",
        });
      }
    } catch (error) {
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      console.error("Failed to delete hotel:", error);
    }
  };

  // Debug logging for hero image
  console.log("Hotel images:", hotel?.images?.length || 0, "images found");
  if (hotel?.images?.length) {
    console.log("Images data:", hotel.images.map(img => ({
      id: img.id,
      url: img.url?.substring(0, 50) + '...', // Truncate URL for readability
      isThumbnail: img.isThumbnail
    })));
  }
  console.log("Selected hero image:", heroImage ? heroImage.substring(0, 50) + '...' : 'None');
  const thumbnailCount = hotel?.images?.filter(img => img.isThumbnail).length || 0;
  console.log(`Thumbnail images: ${thumbnailCount} found`);

  // Remove the duplicate return statement and keep only this one
  return (
    <>
      <HideSidebarItemsWidget />
      <Toaster />
      <div className="flex flex-col gap-6">
        {/* Back button */}
        <div className="px-4">
          <Button
            variant="secondary"
            size="small"
            className="flex items-center gap-1"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            <ChevronLeft className="w-4 h-4" />
            Back to Hotels
          </Button>
        </div>

        {/* Hero section with image background */}
        <div
          className="w-full h-80 overflow-hidden rounded-lg shadow-md flex flex-col justify-between p-6 relative"
          style={{
            backgroundColor: '#f3f4f6', // Fallback background color
          }}
        >
          {/* Hero background image */}
          {heroImage && (
            <>
              <img
                src={heroImage}
                alt="Hero background"
                className="absolute inset-0 w-full h-full object-cover"
                onError={(e) => {
                  console.error("Hero image failed to load:", heroImage);
                  setHeroImageError(true);
                  setHeroImageLoaded(false);
                  e.currentTarget.style.display = 'none';
                }}
                onLoad={() => {
                  console.log("Hero image loaded successfully:", heroImage);
                  setHeroImageLoaded(true);
                  setHeroImageError(false);
                }}
              />
              {/* Dark overlay for better text readability */}
              <div className="absolute inset-0 bg-gradient-to-b from-gray-900/70 to-gray-900/30"></div>
            </>
          )}

          {/* Fallback background when no hero image */}
          {!heroImage && (
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700"></div>
          )}

          {/* Optional: Debug info for missing hero image - remove in production */}
          {process.env.NODE_ENV === 'development' && !heroImage && (
            <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded z-10">
              No Hero Image Found
            </div>
          )}

          {/* Content overlay */}
          <div className="hero-content h-full flex flex-col justify-between relative z-10">
            <div className="flex justify-between items-start">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Badge
                    color={hotel?.is_active ? "green" : "grey"}
                    className="text-xs font-medium px-3 py-1"
                  >
                    {hotel?.is_active ? "Active" : "Inactive"}
                  </Badge>
                  {hotel?.is_featured && (
                    <Badge
                      color="purple"
                      className="text-xs font-medium px-3 py-1"
                    >
                      Featured
                    </Badge>
                  )}
                </div>
                <Heading
                  level="h1"
                  className="text-4xl font-bold text-white drop-shadow-sm"
                >
                  {hotel?.name}
                </Heading>
                <div className="flex flex-wrap items-center gap-4 mt-2">
                  {hotel?.location && (
                    <div className="flex items-center gap-2 text-white/90 bg-black/20 px-3 py-1 rounded-full">
                      <MapPin className="w-4 h-4" />
                      <Text className="text-white/90">{hotel?.location}</Text>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-white/90 bg-black/20 px-3 py-0.5 rounded-full">
                    <Text className="text-white/90 font-bold">{hotel?.rating}</Text>
                    <Rating value={hotel?.rating || 0} readOnly precision={0.5} />
                  </div>
                </div>
              </div>

              <div className="flex gap-x-2 ">
                {/* Edit Button and Drawer */}
                <Button
                  variant="secondary"
                  size="small"
                  className="bg-white/90 hover:bg-white"
                  onClick={() => setOpen(true)}
                >
                  <Edit className="w-4 h-4 mr-1" /> Edit
                </Button>
                <Drawer open={open} onOpenChange={setOpen}>
                  {open && (
                    <div className="bg-black/30 z-40" onClick={() => setOpen(false)} />
                  )}
                  <Drawer.Content className="bg-white shadow-lg h-screen">
                    <Drawer.Header>
                      <span className="text-lg font-semibold">Edit Hotel</span>
                    </Drawer.Header>
                    <Drawer.Body className="flex-1 overflow-y-auto p-6 h-screen">
                      <HotelFormModern
                        formData={{
                          name: hotel?.name ?? "",
                          handle: hotel?.handle ?? "",
                          description: hotel?.description ?? "",
                          is_active: hotel?.is_active ?? false,
                          is_featured: hotel?.is_featured ?? false,
                          is_pets_allowed: hotel?.is_pets_allowed ?? false,
                          destination_id: hotel?.destination_id ?? "",
                          website: hotel?.website ?? null,
                          email: hotel?.email ?? null,
                          phone_number: hotel?.phone_number ?? "",
                          location: hotel?.location ?? "",
                          address: hotel?.address ?? "",
                          check_in_time: hotel?.check_in_time ?? "",
                          check_out_time: hotel?.check_out_time ?? "",
                          currency: hotel?.currency ?? "",
                          rating: hotel?.rating ?? undefined,
                          id: hotel?.id,
                          media: (hotel?.images ?? []).map(
                            (image: {
                              url?: string;
                              id?: string;
                              isThumbnail?: boolean;
                            }) => ({
                              url: image.url ?? "",
                              id: image.id ?? "",
                              isThumbnail: image.isThumbnail ?? false,
                              field_id: crypto.randomUUID(),
                            })
                          ),
                          roomTypes:
                            hotel?.room_types?.map((rt: any) => rt.id) ?? [],
                          notes: hotel?.notes ?? "",
                          tags: hotel?.tags ?? [],
                          amenities: hotel?.amenities ?? [],
                          rules: hotel?.rules ?? [],
                          safety_measures: hotel?.safety_measures ?? [],
                        }}
                        roomTypes={roomTypes}
                        onSubmit={handleUpdate}
                        closeModal={() => setOpen(false)}
                        onSubmitRef={submitRef}
                      />
                    </Drawer.Body>
                    <Drawer.Footer className="flex justify-end gap-2 p-4 border-t">
                      <Button variant="secondary" onClick={() => setOpen(false)}>
                        Cancel
                      </Button>
                      <Button
                        variant="primary"
                        disabled={isUpdating}
                        onClick={async () => {
                          if (submitRef.current) {
                            setIsUpdating(true);
                            try {
                              await submitRef.current();
                            } catch (error) {
                              console.error('Update failed:', error);
                            } finally {
                              setIsUpdating(false);
                            }
                          }
                        }}
                      >
                        {isUpdating ? 'Updating...' : 'Update Hotel'}
                      </Button>
                    </Drawer.Footer>
                  </Drawer.Content>
                </Drawer>
                {/* <Prompt
                  open={deleteOpen}
                  onOpenChange={setDeleteOpen}
                  title="Delete Hotel"
                  description={`Are you sure you want to delete ${hotel?.name}? This action cannot be undone.`}
                  onDelete={handleDelete}
                  trigger={
                    <Button
                      variant="danger"
                      size="small"
                      className="bg-red-500/90 hover:bg-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-1" /> Delete
                    </Button>
                  }
                /> */}
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mt-auto">
              {hotel?.website && (
                <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2">
                  <Globe className="w-4 h-4 text-white" />
                  <Text className="text-white font-medium">
                    {hotel?.website}
                  </Text>
                </div>
              )}

              {hotel?.email && (
                <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2">
                  <Mail className="w-4 h-4 text-white" />
                  <Text className="text-white font-medium">{hotel?.email}</Text>
                </div>
              )}

              {hotel?.phone_number && (
                <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2">
                  <Phone className="w-4 h-4 text-white" />
                  <Text className="text-white font-medium">
                    {hotel?.phone_number}
                  </Text>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Room Management Buttons Section */}
        <Container className="bg-white shadow-md rounded-lg p-4 -mt-6 border border-gray-100">
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2 mb-2">
              <Bed className="w-5 h-5 text-gray-600" />
              <Heading level="h2" className="text-lg font-medium">
                Room Management
              </Heading>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3 w-full">
              <Link
                to={`/hotel-management/hotels/${hotel?.id}/rooms`}
                className="flex flex-col items-center justify-center p-4 bg-green-50 hover:bg-green-100 transition-colors rounded-lg border border-green-100 hover:shadow-md"
              >
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
                  <Bed className="w-6 h-6 text-green-600" />
                </div>
                <Text className="font-medium text-green-700 text-center">
                  All Rooms
                </Text>
                <Text className="text-xs text-green-600/80 text-center mt-1">
                  View all hotel rooms
                </Text>
              </Link>
              <Link
                to={`/hotel-management/hotels/${hotel?.id}/room-configs`}
                className="flex flex-col items-center justify-center p-4 bg-blue-50 hover:bg-blue-100 transition-colors rounded-lg border border-blue-100 hover:shadow-md"
              >
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                  <CreditCard className="w-6 h-6 text-blue-600" />
                </div>
                <Text className="font-medium text-blue-700 text-center">
                  Room Configurations
                </Text>
                <Text className="text-xs text-blue-600/80 text-center mt-1">
                  Manage room types and details
                </Text>
              </Link>
              <Link
                to={`/hotel-management/hotels/${slug}/availability`}
                className="flex flex-col items-center justify-center p-4 bg-purple-50 hover:bg-purple-100 transition-colors rounded-lg border border-purple-100 hover:shadow-md"
              >
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-2">
                  <Calendar className="w-6 h-6 text-purple-600" />
                </div>
                <Text className="font-medium text-purple-700 text-center">
                  Availability
                </Text>
                <Text className="text-xs text-purple-600/80 text-center mt-1">
                  Manage room availability
                </Text>
              </Link>

              <Link
                to={`/hotel-management/hotels/${hotel?.id}/pricing`}
                className="flex flex-col items-center justify-center p-4 bg-amber-50 hover:bg-amber-100 transition-colors rounded-lg border border-amber-100 hover:shadow-md"
              >
                <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mb-2">
                  <Tags className="w-6 h-6 text-amber-600" />
                </div>
                <Text className="font-medium text-amber-700 text-center">
                  Pricing
                </Text>
                <Text className="text-xs text-amber-600/80 text-center mt-1">
                  Set rates and special offers
                </Text>
              </Link>

              <Link
                to={`/hotel-management/bookings?hotel_id=${hotel?.id}`}
                className="flex flex-col items-center justify-center p-4 bg-indigo-50 hover:bg-indigo-100 transition-colors rounded-lg border border-indigo-100 hover:shadow-md"
              >
                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-2">
                  <Calendar className="w-6 h-6 text-indigo-600" />
                </div>
                <Text className="font-medium text-indigo-700 text-center">
                  Bookings
                </Text>
                <Text className="text-xs text-indigo-600/80 text-center mt-1">
                  Manage hotel bookings
                </Text>
              </Link>
            </div>
          </div>
        </Container>

        {/* Main content container */}
        <Container className="mb-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* Details Section */}
            <div className="min-w-0">
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center gap-3 bg-gray-50">
                  <FileText className="w-5 h-5 text-gray-600 flex-shrink-0" />
                  <Heading level="h2" className="text-lg font-medium text-gray-800">
                    Hotel Details
                  </Heading>
                </div>
                <div className="p-6 space-y-6">
                  {/* Description */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <Text className="text-sm text-gray-700 leading-relaxed break-words">
                      {hotel?.description ||
                        "No description available for this hotel."}
                    </Text>
                  </div>

                  {/* Details with icons */}
                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                        <Hash className="w-5 h-5 text-purple-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="text-xs text-gray-500 mb-1">Handle</Text>
                        <Text className="font-medium text-gray-900 break-words">{hotel?.handle}</Text>
                      </div>
                    </div>

                    {hotel?.location && (
                      <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                        <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                          <MapPin className="w-5 h-5 text-red-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <Text className="text-xs text-gray-500 mb-1">
                            Location
                          </Text>
                          <Text className="font-medium text-gray-900 break-words">{hotel?.location}</Text>
                        </div>
                      </div>
                    )}

                    {hotel?.website && (
                      <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                          <Globe className="w-5 h-5 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <Text className="text-xs text-gray-500 mb-1">Website</Text>
                          <a
                            href={hotel?.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-medium text-blue-600 hover:underline break-all"
                          >
                            {hotel?.website}
                          </a>
                        </div>
                      </div>
                    )}

                    {hotel?.email && (
                      <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                        <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                          <Mail className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <Text className="text-xs text-gray-500 mb-1">Email</Text>
                          <a
                            href={`mailto:${hotel?.email}`}
                            className="font-medium text-blue-600 hover:underline break-all"
                          >
                            {hotel?.email}
                          </a>
                        </div>
                      </div>
                    )}

                    {hotel?.phone_number && (
                      <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                        <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0">
                          <Phone className="w-5 h-5 text-yellow-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <Text className="text-xs text-gray-500 mb-1">Phone</Text>
                          <Text className="font-medium text-gray-900 break-words">
                            {hotel?.phone_number}
                          </Text>
                        </div>
                      </div>
                    )}

                    <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0">
                        <Flag className="w-5 h-5 text-indigo-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="text-xs text-gray-500 mb-1">Status</Text>
                        <div className="flex flex-col gap-2">
                          <div className="flex items-center gap-2">
                            {hotel?.is_active ? (
                              <>
                                <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                                <Text className="font-medium text-green-700">Active</Text>
                              </>
                            ) : (
                              <>
                                <XCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
                                <Text className="font-medium text-red-700">Inactive</Text>
                              </>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            {hotel?.is_featured ? (
                              <>
                                <CheckCircle className="w-4 h-4 text-purple-500 flex-shrink-0" />
                                <Text className="font-medium text-purple-700">
                                  Featured
                                </Text>
                              </>
                            ) : (
                              <>
                                <XCircle className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                <Text className="font-medium text-gray-500">
                                  Not Featured
                                </Text>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional details */}
                  {hotel?.notes && (
                    <div className="mt-6">
                      <div className="flex items-center gap-3 mb-3">
                        <FileText className="w-5 h-5 text-gray-600 flex-shrink-0" />
                        <Text className="font-medium text-gray-800">Notes</Text>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <Text className="text-sm text-gray-700 leading-relaxed break-words">
                          {hotel?.notes}
                        </Text>
                      </div>
                    </div>
                  )}
                </div>
              </Container>

              {/* Hotel Policies Section */}
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 mt-6 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center gap-3 bg-gray-50">
                  <Clock className="w-5 h-5 text-gray-600 flex-shrink-0" />
                  <Heading level="h2" className="text-lg font-medium text-gray-800">
                    Hotel Policies
                  </Heading>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                        <LogIn className="w-5 h-5 text-green-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="text-xs text-gray-500 mb-1">
                          Check-in Time
                        </Text>
                        <Text className="font-medium text-gray-900">
                          {hotel?.check_in_time
                            ? (() => {
                              try {
                                const [hours, minutes] =
                                  hotel.check_in_time.split(":");
                                const time = new Date();
                                time.setHours(
                                  parseInt(hours),
                                  parseInt(minutes)
                                );
                                return time.toLocaleTimeString([], {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                });
                              } catch (e) {
                                return hotel.check_in_time;
                              }
                            })()
                            : "Not specified"}
                        </Text>
                      </div>
                    </div>

                    <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                        <LogOut className="w-5 h-5 text-red-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="text-xs text-gray-500 mb-1">
                          Check-out Time
                        </Text>
                        <Text className="font-medium text-gray-900">
                          {hotel?.check_out_time
                            ? (() => {
                              try {
                                const [hours, minutes] =
                                  hotel.check_out_time.split(":");
                                const time = new Date();
                                time.setHours(
                                  parseInt(hours),
                                  parseInt(minutes)
                                );
                                return time.toLocaleTimeString([], {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                });
                              } catch (e) {
                                return hotel.check_out_time;
                              }
                            })()
                            : "Not specified"}
                        </Text>
                      </div>
                    </div>

                    <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-5 h-5 text-purple-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                          />
                        </svg>
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="text-xs text-gray-500 mb-1">
                          Pet Policy
                        </Text>
                        <div className="flex items-center gap-2">
                          {hotel?.is_pets_allowed ? (
                            <>
                              <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                              <Text className="font-medium text-green-700">
                                Pets Allowed
                              </Text>
                            </>
                          ) : (
                            <>
                              <XCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
                              <Text className="font-medium text-red-700">
                                No Pets Allowed
                              </Text>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start gap-4 p-3 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <Shield className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="text-xs text-gray-500 mb-1">
                          Cancellation Policies
                        </Text>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() =>
                              navigate(
                                `/hotel-management/hotels/${slug}/cancellation-policies`
                              )
                            }
                            className="mt-1 text-xs py-1 px-2 h-auto"
                          >
                            Manage Policies
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Container>

              {/* Hotel Images Section */}
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 mt-6 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center justify-between bg-gray-50">
                  <div className="flex items-center gap-3">
                    <Image className="w-5 h-5 text-gray-600 flex-shrink-0" />
                    <Heading level="h2" className="text-lg font-medium text-gray-800">
                      Hotel Images
                    </Heading>
                  </div>
                  {hotel?.images && hotel.images.length > 4 && (
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => setImageGalleryOpen(true)}
                      className="text-sm"
                    >
                      View All ({hotel.images.length})
                    </Button>
                  )}
                </div>
                <div className="p-6">
                  {hotel?.images && hotel.images.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {hotel.images?.slice(0, 6).map(
                        (
                          image: {
                            id?: string;
                            url: string;
                            isThumbnail?: boolean;
                          },
                          index: number
                        ) => (
                          <div
                            key={image.id || `image-${index}`}
                            className="relative group overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer"
                            onClick={() => setImageGalleryOpen(true)}
                          >
                            <img
                              src={image.url}
                              alt={`Hotel image ${index + 1}`}
                              className="w-full h-40 object-cover"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300"></div>
                            {image.isThumbnail && (
                              <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-3 py-1 rounded-full shadow-sm">
                                Featured Image
                              </div>
                            )}
                            {index === 5 && hotel.images && hotel.images.length > 6 && (
                              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <Text className="text-white font-medium text-lg">
                                  +{hotel.images.length - 6} more
                                </Text>
                              </div>
                            )}
                          </div>
                        )
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
                      <Image className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <Text className="text-gray-500">No images available</Text>
                      <Text className="text-gray-400 text-sm">
                        Images can be added when editing the hotel
                      </Text>
                    </div>
                  )}
                </div>
              </Container>
            </div>

            {/* Dashboard Section */}
            <div className="min-w-0">
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden mb-6">
                <div className="px-6 py-4 border-b flex justify-between items-center bg-gray-50">
                  <div className="flex items-center gap-3">
                    <BarChart3 className="w-5 h-5 text-gray-600 flex-shrink-0" />
                    <Heading level="h2" className="text-lg font-medium text-gray-800">
                      Dashboard
                    </Heading>
                  </div>
                </div>
                <div className="p-6 space-y-6">
                  {/* Key Metrics */}
                  <HotelDashboardMetrics hotelId={hotel?.id} />

                  {/* Revenue Chart */}
                  <div className="p-5 border rounded-lg bg-white hover:shadow-sm transition-shadow duration-200">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-5 h-5 text-gray-600" />
                        <Text className="font-medium">Revenue Overview</Text>
                      </div>
                      <select className="text-sm border rounded px-2 py-1">
                        <option>Last 7 days</option>
                        <option>Last 30 days</option>
                        <option>Last 90 days</option>
                      </select>
                    </div>
                    <div className="h-64 w-full bg-gray-50 rounded flex items-center justify-center">
                      <Text className="text-gray-400">
                        Revenue chart visualization would appear here
                      </Text>
                    </div>
                  </div>

                  {/* Additional Dashboard Actions can be added here */}

                  {/* Room Status Breakdown */}
                  <div className="p-5 border rounded-lg bg-white hover:shadow-sm transition-shadow duration-200">
                    <div className="flex items-center gap-2 mb-4">
                      <PieChart className="w-5 h-5 text-gray-600" />
                      <Text className="font-medium">Room Status</Text>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-green-500"></div>
                          <Text className="text-sm">Available</Text>
                        </div>
                        <Text className="text-sm font-medium">12 (22%)</Text>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                          <Text className="text-sm">Occupied</Text>
                        </div>
                        <Text className="text-sm font-medium">35 (64%)</Text>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                          <Text className="text-sm">Reserved</Text>
                        </div>
                        <Text className="text-sm font-medium">5 (9%)</Text>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-red-500"></div>
                          <Text className="text-sm">Maintenance</Text>
                        </div>
                        <Text className="text-sm font-medium">3 (5%)</Text>
                      </div>
                    </div>
                  </div>
                </div>
              </Container>

              {/* Additional dashboard widgets can be added here */}
            </div>
          </div>
        </Container>

        {/* Image Gallery Drawer (replaces FocusModal) */}
        <Drawer open={imageGalleryOpen} onOpenChange={setImageGalleryOpen}>
          <Drawer.Content>
            <Drawer.Header>
              <div className="flex items-center gap-3">
                <Image className="w-5 h-5 text-gray-600" />
                <Heading level="h2" className="text-lg font-medium">
                  {hotel?.name} - All Images ({hotel?.images?.length || 0})
                </Heading>
              </div>
            </Drawer.Header>
            <Drawer.Body className="p-6">
              {hotel?.images && hotel.images.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {hotel.images.map(
                    (
                      image: {
                        id?: string;
                        url: string;
                        isThumbnail?: boolean;
                      },
                      index: number
                    ) => (
                      <div
                        key={image.id || `modal-image-${index}`}
                        className="relative group overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer"
                        onClick={() => window.open(image.url, '_blank')}
                      >
                        <img
                          src={image.url}
                          alt={`Hotel image ${index + 1}`}
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <Text className="text-white font-medium bg-black bg-opacity-50 px-3 py-1 rounded-full text-sm">
                              Click to view full size
                            </Text>
                          </div>
                        </div>
                        {image.isThumbnail && (
                          <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-3 py-1 rounded-full shadow-sm">
                            Featured Image
                          </div>
                        )}
                        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                          {index + 1} of {hotel.images?.length}
                        </div>
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Image className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <Text className="text-gray-500 text-lg">No images available</Text>
                </div>
              )}
            </Drawer.Body>
          </Drawer.Content>
        </Drawer>
      </div>
    </>
  );
};

export default HotelDetailPage;
