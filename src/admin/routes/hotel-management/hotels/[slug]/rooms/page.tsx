import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Badge,
  Input,
  Tooltip,
  Table,
  DropdownMenu,
} from "@camped-ai/ui";
import {
  ChevronLeft,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Bed,
  Users,
  Home,
  Edit,
  Trash2,
  Search,
  Coffee,
  Eye,
  Upload,
  Download,
  Info,
  Map,
  Building2,
  MoreHorizontal,
  Clock,
  Filter,
} from "lucide-react";
import HideSidebarItemsWidget from "../../../../../widgets/hide-sidebar-items-widget";

interface RoomConfig {
  id: string;
  name: string;
  type: string;
  description?: string;
  room_size?: string;
  amenities?: string[];
  bed_type?: string;
  max_extra_beds: number;
  max_cots: number;
  max_adults: number;
  max_adults_beyond_capacity: number;
  max_children: number;
  max_infants: number;
  max_occupancy: number;
  hotel_id: string;
  images?: any[];
  thumbnail?: string;
}

interface Room {
  id: string;
  name: string;
  room_number: string;
  status: string;
  floor: string;
  notes: string;
  is_active: boolean;
  room_config_id: string;
  hotel_id: string;
  options: Record<string, string>;
  inventory_quantity: number;
  created_at?: string;
  updated_at?: string;
  room_config?: RoomConfig; // Associated room configuration
}

interface Hotel {
  id: string;
  name: string;
  description?: string;
  address?: string;
  city?: string;
  country?: string;
  postal_code?: string;
  phone?: string;
  email?: string;
  website?: string;
  destination_id?: string;
  category_id?: string;
  images?: any[];
}

const HotelRoomsOverviewPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRoomType, setSelectedRoomType] = useState<string>("all");
  const [selectedFloor, setSelectedFloor] = useState<string>("all");

  useEffect(() => {
    fetchHotelAndRooms();
  }, [slug]);

  const fetchHotelAndRooms = async () => {
    setIsLoading(true);
    try {
      // For now, use a mock hotel
      const mockHotel: Hotel = {
        id: slug || "hotel_mock",
        name: "",
        description: "A beautiful hotel in the heart of the city",
        address: "123 Main St",
        city: "New York",
        country: "USA",
        postal_code: "10001",
        phone: "************",
        email: "<EMAIL>",
        website: "https://samplehotel.com",
        destination_id: "destination_1",
        category_id: "category_1",
        images: [],
      };

      setHotel(mockHotel);

      // Fetch room configurations
      try {
        console.log(`Fetching room configurations for hotel: ${mockHotel.id}`);
        const timestamp = new Date().getTime();
        const roomConfigsResponse = await fetch(
          `/admin/direct-room-configs?hotel_id=${mockHotel.id}&_=${timestamp}`,
          {
            credentials: "include",
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        let roomConfigsData;
        try {
          roomConfigsData = await roomConfigsResponse.json();
        } catch (jsonError) {
          console.error("Error parsing room configs JSON response:", jsonError);
          throw new Error("Failed to parse room configurations response");
        }

        console.log("Room configurations response:", roomConfigsData);

        if (
          roomConfigsData.roomConfigs &&
          Array.isArray(roomConfigsData.roomConfigs)
        ) {
          const processedRoomConfigs = roomConfigsData.roomConfigs.map(
            (config: any) => ({
              id: config.id || `temp_${Date.now()}`,
              name: config.name || "Unnamed Room Configuration",
              type: config.type || "standard",
              description: config.description || "",
              room_size: config.room_size || "",
              bed_type: config.bed_type || "",
              max_extra_beds: config.max_extra_beds || 0,
              max_cots: config.max_cots || 0,
              max_adults: config.max_adults || 1,
              max_adults_beyond_capacity:
                config.max_adults_beyond_capacity || 0,
              max_children: config.max_children || 0,
              max_infants: config.max_infants || 0,
              max_occupancy: config.max_occupancy || 1,
              amenities: Array.isArray(config.amenities)
                ? config.amenities
                : [],
              hotel_id: config.hotel_id || mockHotel.id,
              images: [],
              thumbnail: config.thumbnail || "",
            })
          );

          setRoomConfigs(processedRoomConfigs);
        } else {
          console.log("No room configurations found, using empty array");
          setRoomConfigs([]);
        }
      } catch (error) {
        console.error("Error fetching room configurations:", error);
        setRoomConfigs([]);
      }

      // Fetch all rooms for this hotel
      try {
        console.log(`Fetching all rooms for hotel: ${mockHotel.id}`);
        const roomsResponse = await fetch(
          `/admin/direct-rooms?hotel_id=${mockHotel.id}`,
          {
            credentials: "include",
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        const roomsData = await roomsResponse.json();
        console.log("Rooms data:", roomsData);

        if (roomsData.rooms && Array.isArray(roomsData.rooms)) {
          setRooms(roomsData.rooms);
        } else {
          setRooms([]);
        }
      } catch (error) {
        console.error("Error fetching rooms:", error);
        toast.error("Error", {
          description: "Failed to fetch rooms.",
        });
        setRooms([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Error", {
        description: "Failed to load hotel and rooms data",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get room config for a room
  const getRoomConfig = (roomConfigId: string): RoomConfig | undefined => {
    return roomConfigs.find((config) => config.id === roomConfigId);
  };

  // Enhanced helper function to get room config from room data (includes embedded config)
  const getRoomConfigFromRoom = (room: Room): RoomConfig | undefined => {
    // First try to use the embedded room_config data from the API
    if (room.room_config) {
      return room.room_config;
    }
    // Fallback to the separate roomConfigs array
    return getRoomConfig(room.room_config_id);
  };

  // Helper function to get badge color for room type
  const getBadgeColorForRoomType = (
    type: string
  ): "green" | "blue" | "purple" | "orange" | "red" | "grey" => {
    switch (type) {
      case "standard":
        return "blue";
      case "deluxe":
        return "green";
      case "suite":
        return "purple";
      case "family":
        return "orange";
      case "executive":
        return "red";
      default:
        return "grey";
    }
  };

  // Get unique room types for filter
  const uniqueRoomTypes = Array.from(
    new Set(roomConfigs.map((config) => config.type))
  );

  // Get unique floors for filter
  const uniqueFloors = Array.from(
    new Set(rooms.map((room) => room.floor).filter(Boolean))
  );

  // Filter rooms based on search and filters
  const filteredRooms = rooms.filter((room) => {
    const roomConfig = getRoomConfigFromRoom(room);
    const searchLower = searchQuery.toLowerCase();

    const matchesSearch =
      room.name.toLowerCase().includes(searchLower) ||
      room.room_number.toLowerCase().includes(searchLower) ||
      room.floor.toLowerCase().includes(searchLower) ||
      room.notes.toLowerCase().includes(searchLower) ||
      roomConfig?.name.toLowerCase().includes(searchLower) ||
      roomConfig?.type.toLowerCase().includes(searchLower);

    const matchesRoomType =
      selectedRoomType === "all" || roomConfig?.type === selectedRoomType;

    const matchesFloor =
      selectedFloor === "all" || room.floor === selectedFloor;

    return matchesSearch && matchesRoomType && matchesFloor;
  });

  return (
    <>
      <HideSidebarItemsWidget />
      <Toaster />
      <div className="space-y-6">
        <Container className="py-6">
          {/* Header with Back Button and Title */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
                className="rounded-full h-9 w-9 p-0 flex items-center justify-center shadow-sm"
              >
                <ChevronLeft />
              </Button>
              <div>
                <Heading level="h1" className="text-2xl font-bold">
                  All Hotel Rooms
                </Heading>
                <Text className="text-gray-500 text-sm">
                  Complete overview of all rooms for {hotel?.name || "this hotel"}
                </Text>
              </div>
            </div>
          </div>

          {/* Stats Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <Text className="text-gray-500 text-sm">Total Rooms</Text>
                  <Heading level="h3" className="text-2xl font-bold">
                    {rooms.length}
                  </Heading>
                </div>
                <div className="bg-blue-50 p-3 rounded-full">
                  <Building2 className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <Text className="text-gray-500 text-sm">Room Types</Text>
                  <Heading level="h3" className="text-2xl font-bold">
                    {roomConfigs.length}
                  </Heading>
                </div>
                <div className="bg-green-50 p-3 rounded-full">
                  <Bed className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <Text className="text-gray-500 text-sm">Total Capacity</Text>
                  <Heading level="h3" className="text-2xl font-bold">
                    {rooms.reduce((sum, room) => {
                      const roomConfig = getRoomConfigFromRoom(room);
                      return sum + (roomConfig?.max_occupancy || 0);
                    }, 0)}{" "}
                    guests
                  </Heading>
                </div>
                <div className="bg-purple-50 p-3 rounded-full">
                  <Users className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <Text className="text-gray-500 text-sm">Floors</Text>
                  <Heading level="h3" className="text-2xl font-bold">
                    {uniqueFloors.length}
                  </Heading>
                </div>
                <div className="bg-amber-50 p-3 rounded-full">
                  <Home className="w-6 h-6 text-amber-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Input
                    placeholder="Search rooms by number, name, floor, or room type..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 h-10 rounded-lg border-gray-300"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedFloor}
                  onChange={(e) => setSelectedFloor(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Floors</option>
                  {uniqueFloors.map((floor) => (
                    <option key={floor} value={floor}>
                      Floor {floor}
                    </option>
                  ))}
                </select>

                {(searchQuery || selectedRoomType !== "all" || selectedFloor !== "all") && (
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => {
                      setSearchQuery("");
                      setSelectedRoomType("all");
                      setSelectedFloor("all");
                    }}
                    className="h-10"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mb-6">
            <Button
              variant="secondary"
              onClick={() =>
                navigate(`/hotel-management/hotels/${slug}/room-configs`)
              }
              className="flex items-center gap-2"
            >
              <Bed className="w-4 h-4" />
              Manage Room Types
            </Button>
            <Button
              variant="secondary"
              onClick={() =>
                navigate(`/hotel-management/hotels/${slug}/availability`)
              }
              className="flex items-center gap-2"
            >
              <Clock className="w-4 h-4" />
              Manage Availability
            </Button>
            <Button
              variant="secondary"
              onClick={() =>
                navigate(`/hotel-management/hotels/${slug}/floor-plan`)
              }
              className="flex items-center gap-2"
            >
              <Map className="w-4 h-4" />
              Floor Plan
            </Button>
          </div>

          {/* Rooms Display */}
          {isLoading ? (
            <div className="animate-pulse space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="bg-gray-100 h-32 rounded-lg"></div>
              ))}
            </div>
          ) : filteredRooms.length === 0 ? (
            <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
              <div className="flex flex-col items-center justify-center py-8">
                <div className="bg-blue-50 p-4 rounded-full mb-4">
                  {searchQuery || selectedRoomType !== "all" || selectedFloor !== "all" ? (
                    <Search className="w-8 h-8 text-blue-600" />
                  ) : (
                    <Building2 className="w-8 h-8 text-blue-600" />
                  )}
                </div>
                <Heading level="h3" className="text-xl font-semibold mb-2">
                  {searchQuery || selectedRoomType !== "all" || selectedFloor !== "all"
                    ? "No matching rooms found"
                    : "No rooms found"}
                </Heading>
                <Text className="text-gray-500 mb-6 max-w-md">
                  {searchQuery || selectedRoomType !== "all" || selectedFloor !== "all"
                    ? "Try adjusting your search criteria or filters."
                    : "No rooms have been created for this hotel yet. Start by creating room configurations and then add individual rooms."}
                </Text>
                {!(searchQuery || selectedRoomType !== "all" || selectedFloor !== "all") && (
                  <Button
                    variant="primary"
                    onClick={() =>
                      navigate(`/hotel-management/hotels/${slug}/room-configs`)
                    }
                    className="rounded-lg shadow-sm"
                  >
                    <Bed className="w-4 h-4 mr-2" />
                    Create Room Configurations
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div className="border-b border-gray-200 px-5 py-4">
                <Heading level="h2" className="text-lg font-semibold">
                  Rooms ({filteredRooms.length})
                </Heading>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <Table.Header>
                    <Table.Row>
                      <Table.HeaderCell>Room Details</Table.HeaderCell>
                      <Table.HeaderCell>Room Type</Table.HeaderCell>
                      <Table.HeaderCell>Capacity</Table.HeaderCell>
                      {/* <Table.HeaderCell>Amenities</Table.HeaderCell> */}
                      <Table.HeaderCell>Floor</Table.HeaderCell>
                      {/* <Table.HeaderCell>Notes</Table.HeaderCell> */}
                      <Table.HeaderCell>Actions</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {filteredRooms.map((room) => {
                      const roomConfig = getRoomConfigFromRoom(room);
                      return (
                        <Table.Row key={room.id}>
                          <Table.Cell>
                            <div className="flex items-center gap-3">
                              <div className="bg-blue-50 p-2 rounded-lg">
                                <Bed className="w-5 h-5 text-blue-600" />
                              </div>
                              <div>
                                <Text className="font-medium text-gray-900">
                                  {room.room_number}
                                </Text>
                                <Text className="text-sm text-gray-500">
                                  {room.name}
                                </Text>
                              </div>
                            </div>
                          </Table.Cell>

                          <Table.Cell>
                            <div className="space-y-1">
                              <Text className="text-xs text-gray-500 block">
                                {roomConfig?.name || "Unknown Configuration"}
                              </Text>
                            </div>
                          </Table.Cell>

                          <Table.Cell>
                            <div className="space-y-1">
                              <div className="flex items-center gap-1">
                                <Users className="w-4 h-4 text-gray-400" />
                                <Text className="text-sm font-medium">
                                  {roomConfig?.max_occupancy || 0} guests
                                </Text>
                              </div>
                              <Text className="text-xs text-gray-500">
                                {roomConfig?.max_adults || 0} adults, {roomConfig?.max_children || 0} children
                              </Text>
                            </div>
                          </Table.Cell>

                          {/* <Table.Cell>
                            <div className="space-y-1">
                              {roomConfig?.amenities && roomConfig.amenities.length > 0 ? (
                                roomConfig.amenities.length <= 2 ? (
                                  <Text className="text-sm">
                                    {roomConfig.amenities.join(", ")}
                                  </Text>
                                ) : (
                                  <Tooltip content={roomConfig.amenities.join(", ")}>
                                    <div className="flex items-center gap-1 cursor-help">
                                      <Coffee className="w-4 h-4 text-gray-400" />
                                      <Text className="text-sm">
                                        {roomConfig.amenities.length} amenities
                                      </Text>
                                    </div>
                                  </Tooltip>
                                )
                              ) : (
                                <Text className="text-sm text-gray-400">None</Text>
                              )}
                              {roomConfig?.bed_type && (
                                <Text className="text-xs text-gray-500">
                                  {roomConfig.bed_type.charAt(0).toUpperCase() +
                                   roomConfig.bed_type.slice(1)} bed
                                </Text>
                              )}
                            </div>
                          </Table.Cell> */}

                          <Table.Cell>
                            <Badge color="grey" className="font-normal">
                              Floor {room.floor || "N/A"}
                            </Badge>
                          </Table.Cell>

                          {/* <Table.Cell>
                            <Text className="text-sm text-gray-600 truncate max-w-[150px]">
                              {room.notes || "-"}
                            </Text>
                          </Table.Cell> */}

                          <Table.Cell>
                            <DropdownMenu>
                              <DropdownMenu.Trigger asChild>
                                <Button
                                  variant="secondary"
                                  size="small"
                                  className="h-8 w-8 p-0"
                                >
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenu.Trigger>
                              <DropdownMenu.Content align="end">
                                <DropdownMenu.Item
                                  onClick={() =>
                                    navigate(
                                      `/hotel-management/hotels/${slug}/room-configs/${room.room_config_id}/rooms`
                                    )
                                  }
                                >
                                  <Eye className="w-4 h-4 mr-2" />
                                  View Room Details
                                </DropdownMenu.Item>
                                <DropdownMenu.Item
                                  onClick={() =>
                                    navigate(
                                      `/hotel-management/hotels/${slug}/availability?room=${room.id}`
                                    )
                                  }
                                >
                                  <Clock className="w-4 h-4 mr-2" />
                                  Manage Availability
                                </DropdownMenu.Item>
                                <DropdownMenu.Separator />
                                <DropdownMenu.Item
                                  onClick={() =>
                                    navigate(
                                      `/hotel-management/hotels/${slug}/room-configs/${room.room_config_id}`
                                    )
                                  }
                                >
                                  <Bed className="w-4 h-4 mr-2" />
                                  Edit Room Type
                                </DropdownMenu.Item>
                              </DropdownMenu.Content>
                            </DropdownMenu>
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              </div>
            </div>
          )}
        </Container>
      </div>
    </>
  );
};

export const config = defineRouteConfig({
  label: "All Rooms",
  icon: Building2,
});

export default HotelRoomsOverviewPage;
